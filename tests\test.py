import json
from collections import deque

# ANSI color codes for colored output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'  # End color formatting

class CircuitManager:
    """Loads and stores all circuit definitions from a JSON source."""
    def __init__(self, circuit_definitions):
        self.circuits = {c['gate_name']: c for c in circuit_definitions}

    def get_circuit(self, name):
        """Retrieves a circuit definition by its name."""
        circuit = self.circuits.get(name)
        if not circuit:
            raise ValueError(f"Circuit definition for '{name}' not found.")
        return circuit

class LogicGateSimulator:
    """
    Simulates a digital logic circuit hierarchically. It handles combinatorial
    and sequential logic, including feedback loops and rising-edge clock triggers.
    """
    PRIMARY_GATES = {"AND", "OR", "NOT", "XOR", "XNOR", "NAND", "NOR", "JK", "T"}

    def __init__(self, circuit_def, manager):
        """
        Initializes the simulator for a specific circuit definition.

        Args:
            circuit_def (dict): The definition for this specific circuit instance.
            manager (CircuitManager): The manager that holds all circuit blueprints.
        """
        self.circuit_def = circuit_def
        self.manager = manager
        self.gate_name = self.circuit_def.get("gate_name", "Unnamed")

        self.wires = {}
        self.prev_clk_states = {}
        self.sub_simulators = {}
        self.flip_flop_states = {}  # Store Q states for JK and T flip-flops

        self._initialize_circuit()

    def _initialize_circuit(self):
        """
        Initializes wires, clocks, and sub-circuit simulators for this circuit level.
        """
        # Initialize all known wires for this circuit to False
        for pin in self.circuit_def.get("gate_inputs", []):
            self.wires[pin] = False
        for pin in self.circuit_def.get("gate_outputs", []):
            self.wires[pin] = False

        # Initialize wires from nodes
        for node in self.circuit_def.get("nodes", []):
            if "output" in node:  # Single output node
                self.wires[node["output"]] = False
            if "outputs" in node:  # Multi-output node
                # The outputs dict maps node_id to parent wire names
                for wire_name in node["outputs"].values():
                    # Initialize all wires to False initially
                    self.wires[wire_name] = False

        # Special handling for constant generation wires
        if self.gate_name == "COUNTER_4BIT":
            self.wires["a"] = False
            self.wires["a_n"] = True
            self.wires["const_1"] = True

        # Instantiate sub-simulators and identify clock pins
        for node in self.circuit_def.get("nodes", []):
            node_type = node["type"]
            node_id = node['id']

            if node_type not in self.PRIMARY_GATES:
                sub_def = self.manager.get_circuit(node_type)
                self.sub_simulators[node_id] = LogicGateSimulator(sub_def, self.manager)
            elif node_type in ("JK", "T"):
                clk_pin = node["inputs"][0]
                # Always initialize clock states to False for proper edge detection
                self.prev_clk_states[clk_pin] = False
                # Initialize flip-flop state
                self.flip_flop_states[node_id] = False

                # Set initial flip-flop outputs
                if "outputs" in node:
                    for output_type, wire_name in node["outputs"].items():
                        if output_type == "q":
                            self.wires[wire_name] = False
                        elif output_type == "qn":
                            self.wires[wire_name] = True

        # Special initialization for SR latch - need to stabilize the circuit
        if self.gate_name == "SR_FLIP_FLOP":
            # Set initial state for SR latch: q=False, qn=True (reset state)
            self.wires["q"] = False
            self.wires["qn"] = True
            # Run initial stabilization with no inputs to get proper initial state
            self._stabilize_circuit()

        # Reset all clock states to False after any stabilization to ensure proper edge detection
        # This is crucial for proper edge detection in sequential circuits
        for clk_pin in list(self.prev_clk_states.keys()):
            self.prev_clk_states[clk_pin] = False

        # For counter, ensure all flip-flops start with Q=False
        if self.gate_name == "COUNTER_4BIT":
            for node in self.circuit_def.get("nodes", []):
                if node["type"] == "T":
                    node_id = node["id"]
                    self.flip_flop_states[node_id] = False
                    if "outputs" in node:
                        for output_type, wire_name in node["outputs"].items():
                            if output_type == "q":
                                self.wires[wire_name] = False
                            elif output_type == "qn":
                                self.wires[wire_name] = True



    def _stabilize_circuit(self):
        """Stabilize the circuit by iterating until no changes occur."""
        max_iterations = 100
        for iteration in range(max_iterations):
            changed_in_iteration = False
            for node in self.circuit_def.get("nodes", []):
                if self._update_node_outputs(node, set()):
                    changed_in_iteration = True
            if not changed_in_iteration:
                break

    def _stabilize_circuit_with_edges(self, rising_edges):
        """Stabilize the circuit with rising edge information."""
        max_iterations = 100
        for iteration in range(max_iterations):
            changed_in_iteration = False
            for node in self.circuit_def.get("nodes", []):
                if self._update_node_outputs(node, rising_edges):
                    changed_in_iteration = True
            if not changed_in_iteration:
                break

    def execute(self, inputs):
        """
        Executes the logic circuit simulation for one time step.

        Args:
            inputs (dict): Primary input pins and their boolean values for this level.

        Returns:
            dict: The state of the primary output pins for this level.
        """
        # Update input wires
        for pin_name, value in inputs.items():
            if pin_name in self.wires:
                self.wires[pin_name] = value

        # Special handling for constant generation (COUNTER_4BIT)
        if self.gate_name == "COUNTER_4BIT":
            self.wires["a"] = False
            self.wires["a_n"] = True
            self.wires["const_1"] = True

        # Detect rising edges
        rising_edges = set()
        for clk_pin, prev_state in self.prev_clk_states.items():
            current_state = self.wires.get(clk_pin, False)
            if current_state and not prev_state:
                rising_edges.add(clk_pin)

        # Iteratively stabilize the circuit to handle feedback loops
        self._stabilize_circuit_with_edges(rising_edges)

        # Update previous clock states
        for clk_pin in self.prev_clk_states:
            self.prev_clk_states[clk_pin] = self.wires.get(clk_pin, False)

        # Clear the updated flip-flops set for next execution
        if hasattr(self, '_updated_flip_flops'):
            self._updated_flip_flops.clear()

        # Return output values
        return {pin: self.wires.get(pin, False) for pin in self.circuit_def.get("gate_outputs", [])}

    def _update_node_outputs(self, node, rising_edges):
        """
        Calculates and updates the output(s) of a single node.
        Returns True if any wire value changed.
        """
        node_type = node["type"]
        node_id = node['id']
        input_vals = [self.wires.get(pin, False) for pin in node["inputs"]]
        changed = False

        if node_type in self.PRIMARY_GATES:
            new_vals = {}
            
            if node_type in ("AND", "OR", "NOT", "XOR", "XNOR", "NAND", "NOR"):
                # Combinatorial gates
                output_wire = node.get("output")
                if node_type == "AND": 
                    new_vals[output_wire] = all(input_vals)
                elif node_type == "OR": 
                    new_vals[output_wire] = any(input_vals)
                elif node_type == "NOT": 
                    new_vals[output_wire] = not input_vals[0]
                elif node_type == "XOR": 
                    new_vals[output_wire] = sum(input_vals) % 2 == 1
                elif node_type == "XNOR": 
                    new_vals[output_wire] = sum(input_vals) % 2 == 0
                elif node_type == "NAND": 
                    new_vals[output_wire] = not all(input_vals)
                elif node_type == "NOR": 
                    new_vals[output_wire] = not any(input_vals)
                    
            elif node_type in ("T", "JK"):
                # Sequential gates (flip-flops)
                clk_pin = node["inputs"][0]
                outputs = node["outputs"]
                q_wire = outputs.get("q")
                qn_wire = outputs.get("qn")
                
                # Get current Q state from our stored state
                current_q = self.flip_flop_states.get(node_id, False)

                # Only update flip-flops on rising edges, and only once per edge
                if clk_pin in rising_edges and not hasattr(self, '_updated_flip_flops'):
                    self._updated_flip_flops = set()

                if clk_pin in rising_edges and node_id not in getattr(self, '_updated_flip_flops', set()):
                    if node_type == "T":
                        t = input_vals[1] if len(input_vals) > 1 else False
                        if t:
                            new_q = not current_q
                        else:
                            new_q = current_q
                        self.flip_flop_states[node_id] = new_q
                        new_vals[q_wire] = new_q
                        new_vals[qn_wire] = not new_q
                        # Mark this flip-flop as updated
                        if not hasattr(self, '_updated_flip_flops'):
                            self._updated_flip_flops = set()
                        self._updated_flip_flops.add(node_id)
                    elif node_type == "JK":
                        j = input_vals[1] if len(input_vals) > 1 else False
                        k = input_vals[2] if len(input_vals) > 2 else False



                        if j and k:
                            new_q = not current_q
                        elif j:
                            new_q = True
                        elif k:
                            new_q = False
                        else:
                            new_q = current_q

                        self.flip_flop_states[node_id] = new_q
                        new_vals[q_wire] = new_q
                        new_vals[qn_wire] = not new_q
                        # Mark this flip-flop as updated
                        if not hasattr(self, '_updated_flip_flops'):
                            self._updated_flip_flops = set()
                        self._updated_flip_flops.add(node_id)
                else:
                    # No clock edge, maintain current state
                    new_vals[q_wire] = current_q
                    new_vals[qn_wire] = not current_q
            
            for wire, val in new_vals.items():
                if wire and self.wires.get(wire) != val:
                    self.wires[wire] = val
                    changed = True
                    
        else:  # Sub-circuit
            sub_sim = self.sub_simulators[node_id]
            sub_inputs_def = sub_sim.circuit_def.get("gate_inputs", [])
            
            # Map parent circuit's wires to sub-circuit's inputs
            sub_inputs = {}
            for i, input_pin in enumerate(node["inputs"]):
                if i < len(sub_inputs_def):
                    sub_inputs[sub_inputs_def[i]] = self.wires.get(input_pin, False)
            
            # Execute sub-circuit
            sub_outputs = sub_sim.execute(sub_inputs)
            
            # Map sub-circuit outputs back to parent circuit wires
            for local_pin, parent_wire in node["outputs"].items():
                # Find corresponding output from sub-circuit
                sub_output_pins = sub_sim.circuit_def.get("gate_outputs", [])
                for sub_pin in sub_output_pins:
                    if sub_pin == local_pin:
                        new_val = sub_outputs.get(sub_pin, False)
                        if self.wires.get(parent_wire) != new_val:
                            self.wires[parent_wire] = new_val
                            changed = True
                        break
                        
        return changed


def run_comprehensive_tests():
    """Run comprehensive tests with pass/fail reporting"""
    
    # JSON content embedded directly
    circuits_json_string = """
    [
        {
            "gate_name": "SR_FLIP_FLOP",
            "gate_inputs": ["s", "r"],
            "gate_outputs": ["q", "qn"],
            "nodes": [
                { "id": "n1", "type": "NOR", "inputs": ["r", "qn"], "output": "q" },
                { "id": "n2", "type": "NOR", "inputs": ["s", "q"], "output": "qn" }
            ]
        },
        {
            "gate_name": "D_FLIP_FLOP",
            "gate_inputs": ["clk", "d"],
            "gate_outputs": ["q", "qn"],
            "nodes": [
                { "id": "inv1", "type": "NOT", "inputs": ["d"], "output": "d_n" },
                { "id": "jk1", "type": "JK", "inputs": ["clk", "d", "d_n"], "outputs": {"q": "q", "qn": "qn"} }
            ]
        },
        {
            "gate_name": "HALF_ADDER",
            "gate_inputs": ["a", "b"],
            "gate_outputs": ["sum", "carry"],
            "nodes": [
                { "id": "xor1", "type": "XOR", "inputs": ["a", "b"], "output": "sum" },
                { "id": "and1", "type": "AND", "inputs": ["a", "b"], "output": "carry" }
            ]
        },
        {
            "gate_name": "FULL_ADDER",
            "gate_inputs": ["a", "b", "cin"],
            "gate_outputs": ["sum", "cout"],
            "nodes": [
                { "id": "ha1", "type": "HALF_ADDER", "inputs": ["a", "b"], "outputs": {"sum": "s1", "carry": "c1"} },
                { "id": "ha2", "type": "HALF_ADDER", "inputs": ["s1", "cin"], "outputs": {"sum": "sum", "carry": "c2"} },
                { "id": "or1", "type": "OR", "inputs": ["c1", "c2"], "output": "cout" }
            ]
        },
        {
            "gate_name": "4_BIT_ADDER",
            "gate_inputs": ["a0", "a1", "a2", "a3", "b0", "b1", "b2", "b3", "cin"],
            "gate_outputs": ["s0", "s1", "s2", "s3", "cout"],
            "nodes": [
                { "id": "fa0", "type": "FULL_ADDER", "inputs": ["a0", "b0", "cin"], "outputs": {"sum": "s0", "cout": "c0"} },
                { "id": "fa1", "type": "FULL_ADDER", "inputs": ["a1", "b1", "c0"], "outputs": {"sum": "s1", "cout": "c1"} },
                { "id": "fa2", "type": "FULL_ADDER", "inputs": ["a2", "b2", "c1"], "outputs": {"sum": "s2", "cout": "c2"} },
                { "id": "fa3", "type": "FULL_ADDER", "inputs": ["a3", "b3", "c2"], "outputs": {"sum": "s3", "cout": "cout"} }
            ]
        },
        {
            "gate_name": "COUNTER_4BIT",
            "gate_inputs": ["clk"],
            "gate_outputs": ["q0", "q1", "q2", "q3"],
            "nodes": [
                { "id": "xor_true", "type": "XOR", "inputs": ["a", "a_n"], "output": "const_1" },
                { "id": "not_a", "type": "NOT", "inputs": ["a"], "output": "a_n" },
                { "id": "ff0", "type": "T", "inputs": ["clk", "const_1"], "outputs": {"q": "q0", "qn": "q0n"} },
                { "id": "ff1", "type": "T", "inputs": ["clk", "q0"], "outputs": {"q": "q1", "qn": "q1n"} },
                { "id": "and1", "type": "AND", "inputs": ["q0", "q1"], "output": "t2_in" },
                { "id": "ff2", "type": "T", "inputs": ["clk", "t2_in"], "outputs": {"q": "q2", "qn": "q2n"} },
                { "id": "and2", "type": "AND", "inputs": ["t2_in", "q2"], "output": "t3_in" },
                { "id": "ff3", "type": "T", "inputs": ["clk", "t3_in"], "outputs": {"q": "q3", "qn": "q3n"} }
            ]
        },
        {
            "gate_name": "SHIFT_REGISTER_4BIT",
            "gate_inputs": ["clk", "sin"],
            "gate_outputs": ["q0", "q1", "q2", "q3"],
            "nodes": [
                { "id": "dff0", "type": "D_FLIP_FLOP", "inputs": ["clk", "sin"], "outputs": {"q": "q0", "qn": "q0n"} },
                { "id": "dff1", "type": "D_FLIP_FLOP", "inputs": ["clk", "q0"], "outputs": {"q": "q1", "qn": "q1n"} },
                { "id": "dff2", "type": "D_FLIP_FLOP", "inputs": ["clk", "q1"], "outputs": {"q": "q2", "qn": "q2n"} },
                { "id": "dff3", "type": "D_FLIP_FLOP", "inputs": ["clk", "q2"], "outputs": {"q": "q3", "qn": "q3n"} }
            ]
        }
    ]
    """
    
    all_circuits_json = json.loads(circuits_json_string)
    manager = CircuitManager(all_circuits_json)
    
    total_tests = 0
    passed_tests = 0
    
    print(f"{Colors.BOLD}{Colors.CYAN}{'=' * 60}")
    print("COMPREHENSIVE LOGIC GATE SIMULATOR TESTS")
    print(f"{'=' * 60}{Colors.END}")

    # Test 0: Simple JK Flip-Flop
    print(f"\n{Colors.BOLD}{Colors.BLUE}--- Test 0: JK Flip-Flop ---{Colors.END}")

    # Create a simple JK flip-flop test circuit
    jk_test_def = {
        "gate_name": "JK_TEST",
        "gate_inputs": ["clk", "j", "k"],
        "gate_outputs": ["q", "qn"],
        "nodes": [
            { "id": "jk1", "type": "JK", "inputs": ["clk", "j", "k"], "outputs": {"q": "q", "qn": "qn"} }
        ]
    }

    jk_test = LogicGateSimulator(jk_test_def, manager)

    jk_test_cases = [
        ({"clk": False, "j": True, "k": False}, {"q": False, "qn": True}, "Setup J=1, K=0"),
        ({"clk": True, "j": True, "k": False}, {"q": True, "qn": False}, "Set: J=1, K=0"),
        ({"clk": False, "j": False, "k": True}, {"q": True, "qn": False}, "Setup J=0, K=1"),
        ({"clk": True, "j": False, "k": True}, {"q": False, "qn": True}, "Reset: J=0, K=1"),
        ({"clk": False, "j": True, "k": True}, {"q": False, "qn": True}, "Setup J=1, K=1"),
        ({"clk": True, "j": True, "k": True}, {"q": True, "qn": False}, "Toggle: J=1, K=1"),
    ]

    for inputs, expected, description in jk_test_cases:
        result = jk_test.execute(inputs)
        total_tests += 1
        if result == expected:
            passed_tests += 1
            print(f"{Colors.GREEN}✓ {description}: {result}{Colors.END}")
        else:
            print(f"{Colors.RED}✗ {description}: Expected {expected}, got {result}{Colors.END}")

    # Test 1: SR Latch
    print(f"\n{Colors.BOLD}{Colors.BLUE}--- Test 1: SR Latch ---{Colors.END}")
    sr_latch_def = manager.get_circuit("SR_FLIP_FLOP")
    sr_latch = LogicGateSimulator(sr_latch_def, manager)
    
    test_cases = [
        ({"s": False, "r": False}, {"q": False, "qn": True}, "Initial state"),
        ({"s": True, "r": False}, {"q": True, "qn": False}, "Set state"),
        ({"s": False, "r": False}, {"q": True, "qn": False}, "Hold after set"),
        ({"s": False, "r": True}, {"q": False, "qn": True}, "Reset state"),
        ({"s": False, "r": False}, {"q": False, "qn": True}, "Hold after reset"),
    ]
    
    for inputs, expected, description in test_cases:
        result = sr_latch.execute(inputs)
        total_tests += 1
        if result == expected:
            passed_tests += 1
            print(f"{Colors.GREEN}✓ {description}: {result}{Colors.END}")
        else:
            print(f"{Colors.RED}✗ {description}: Expected {expected}, got {result}{Colors.END}")
    
    # Test 2: Half Adder
    print(f"\n{Colors.BOLD}{Colors.BLUE}--- Test 2: Half Adder ---{Colors.END}")
    ha_def = manager.get_circuit("HALF_ADDER")
    ha = LogicGateSimulator(ha_def, manager)
    
    test_cases = [
        ({"a": False, "b": False}, {"sum": False, "carry": False}, "0 + 0"),
        ({"a": True, "b": False}, {"sum": True, "carry": False}, "1 + 0"),
        ({"a": False, "b": True}, {"sum": True, "carry": False}, "0 + 1"),
        ({"a": True, "b": True}, {"sum": False, "carry": True}, "1 + 1"),
    ]
    
    for inputs, expected, description in test_cases:
        result = ha.execute(inputs)
        total_tests += 1
        if result == expected:
            passed_tests += 1
            print(f"{Colors.GREEN}✓ {description}: {result}{Colors.END}")
        else:
            print(f"{Colors.RED}✗ {description}: Expected {expected}, got {result}{Colors.END}")
    
    # Test 3: 4-Bit Adder
    print(f"\n{Colors.BOLD}{Colors.BLUE}--- Test 3: 4-Bit Adder ---{Colors.END}")
    adder_def = manager.get_circuit("4_BIT_ADDER")
    adder = LogicGateSimulator(adder_def, manager)
    
    def to_binary_inputs(a, b, cin=False):
        inputs = {"cin": cin}
        for i in range(4):
            inputs[f"a{i}"] = bool((a >> i) & 1)
            inputs[f"b{i}"] = bool((b >> i) & 1)
        return inputs
    
    def from_binary_outputs(outputs):
        result = 0
        for i in range(4):
            if outputs[f"s{i}"]:
                result |= (1 << i)
        return result, outputs["cout"]
    
    test_cases = [
        (5, 3, False, 8, False, "5 + 3"),
        (15, 1, False, 0, True, "15 + 1 (overflow)"),
        (7, 7, False, 14, False, "7 + 7"),
        (8, 8, False, 0, True, "8 + 8 (overflow)"),
        (0, 0, True, 1, False, "0 + 0 + carry"),
    ]
    
    for a, b, cin, expected_sum, expected_cout, description in test_cases:
        inputs = to_binary_inputs(a, b, cin)
        outputs = adder.execute(inputs)
        result_sum, result_cout = from_binary_outputs(outputs)
        total_tests += 1
        if result_sum == expected_sum and result_cout == expected_cout:
            passed_tests += 1
            print(f"{Colors.GREEN}✓ {description} = {result_sum}, carry={result_cout}{Colors.END}")
        else:
            print(f"{Colors.RED}✗ {description}: Expected {expected_sum} carry={expected_cout}, got {result_sum} carry={result_cout}{Colors.END}")
    
    # Test 4: 4-Bit Counter
    print(f"\n{Colors.BOLD}{Colors.BLUE}--- Test 4: 4-Bit Counter ---{Colors.END}")
    counter_def = manager.get_circuit("COUNTER_4BIT")
    counter = LogicGateSimulator(counter_def, manager)

    print(f"{Colors.YELLOW}Testing first 16 counts:{Colors.END}")

    # Check initial state before any clock edges
    initial_outputs = counter.execute({'clk': False})
    initial_q = [initial_outputs['q0'], initial_outputs['q1'], initial_outputs['q2'], initial_outputs['q3']]
    initial_decimal = sum((1 << j) if initial_q[j] else 0 for j in range(4))
    print(f"Initial state (no clock): Binary={list(map(int, initial_q))} Decimal={initial_decimal}")

    for i in range(16):
        # Falling edge first to ensure we start from low
        if i > 0:  # Skip first falling edge since we start from low
            counter.execute({'clk': False})

        # Rising edge - this should trigger the flip-flops
        outputs = counter.execute({'clk': True})
        q = [outputs['q0'], outputs['q1'], outputs['q2'], outputs['q3']]
        decimal = sum((1 << j) if q[j] else 0 for j in range(4))

        total_tests += 1
        if decimal == i:
            passed_tests += 1
            print(f"{Colors.GREEN}✓ Cycle {i:2}: Binary={list(map(int, q))} Decimal={decimal}{Colors.END}")
        else:
            print(f"{Colors.RED}✗ Cycle {i:2}: Expected {i}, got {decimal} (Binary={list(map(int, q))}){Colors.END}")
    
    # Test 5: 4-Bit Shift Register
    print(f"\n{Colors.BOLD}{Colors.BLUE}--- Test 5: 4-Bit Shift Register ---{Colors.END}")
    shifter_def = manager.get_circuit("SHIFT_REGISTER_4BIT")
    shifter = LogicGateSimulator(shifter_def, manager)
    
    # Pattern to shift: 1011
    shift_pattern = [True, False, True, True]
    expected_states = [
        [False, False, False, True],   # After shifting in 1
        [False, False, True, False],   # After shifting in 0
        [False, True, False, True],    # After shifting in 1
        [True, False, True, True],     # After shifting in 1
        [False, True, True, False],    # After shifting in 0 (pattern done)
        [True, True, False, False],    # After shifting in 0
        [True, False, False, False],   # After shifting in 0
        [False, False, False, False],  # After shifting in 0
    ]
    
    print(f"{Colors.YELLOW}Shifting pattern 1011 then zeros:{Colors.END}")
    for i in range(8):
        bit_in = shift_pattern.pop(0) if shift_pattern else False
        
        # Rising edge
        outputs = shifter.execute({'clk': True, 'sin': bit_in})
        q = [outputs['q3'], outputs['q2'], outputs['q1'], outputs['q0']]
        
        total_tests += 1
        if q == expected_states[i]:
            passed_tests += 1
            print(f"{Colors.GREEN}✓ Cycle {i+1} (In={int(bit_in)}): {list(map(int, q))}{Colors.END}")
        else:
            print(f"{Colors.RED}✗ Cycle {i+1} (In={int(bit_in)}): Expected {list(map(int, expected_states[i]))}, got {list(map(int, q))}{Colors.END}")
        
        # Falling edge
        shifter.execute({'clk': False, 'sin': bit_in})
    
    # Final Summary
    print(f"\n{Colors.BOLD}{Colors.CYAN}{'=' * 60}")
    success_rate = (passed_tests/total_tests)*100
    if success_rate == 100:
        print(f"{Colors.GREEN}TEST SUMMARY: {passed_tests}/{total_tests} tests passed")
        print(f"Success Rate: {success_rate:.1f}%{Colors.END}")
    elif success_rate >= 80:
        print(f"{Colors.YELLOW}TEST SUMMARY: {passed_tests}/{total_tests} tests passed")
        print(f"Success Rate: {success_rate:.1f}%{Colors.END}")
    else:
        print(f"{Colors.RED}TEST SUMMARY: {passed_tests}/{total_tests} tests passed")
        print(f"Success Rate: {success_rate:.1f}%{Colors.END}")
    print(f"{Colors.CYAN}{'=' * 60}{Colors.END}")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    success = run_comprehensive_tests()
    exit(0 if success else 1)