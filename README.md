# ✅ Digital Circuit Simulator Development Checklist (Godot)

## 1. 🔧 Project Setup
- [x] Add global singleton `SimulationManager.gd` for managing updates

## 2. 🧱 Core Node Architecture
- [x] `Gate2D` (Base class for gates)
  - [x] Handles pins, position, evaluation
  - [x] Generate Pins
  - [x] Generate Shape
- [x] `Pin2D`
  - [x] Position, type (`input`, `output`), and state (`true`, `false`)
- [x] `Wire2D`
  - [x] Connects output to input
  - [x] Changes color based on signal state
  - [x] Delete Wire
- [x] `CanvasNode`
  - [x] Handles drag & drop
  - [x] grid snapping
  - [x] zoom
  - [x] pan
- [x] `GateLibrary`
  - [x] UI panel for drag-and-drop component spawning

## 3. 🧠 Logic Simulation System
- [x] Each gate evaluates logic based on inputs
- [x] Use either:
  - [x] Signal-based propagation (input change triggers output update)
- [x] Detect and warn of feedback loops (optional)

## 4. 🔌 Basic Gates (Extend `Gate2D`)
- [x] AND Gate
- [x] OR Gate
- [x] NOT Gate
- [x] NAND Gate
- [x] NOR Gate
- [x] XOR Gate
- [x] XNOR Gate
- [x] Switch
- [x] LED / Output Probe
- [x] CLK

## 5. 🖱️ User Interaction System
- [x] Drag and drop gates onto canvas
- [x] Click and drag from pin to pin to draw wires
- [x] Hover/click wires to delete
- [x] Pan canvas (middle mouse or right-drag)
- [x] Zoom in/out with mouse wheel

## 6. 🧩 Advanced Components
- [x] SR Latch
- [x] SR Flip-Flop
- [x] D Flip-Flop
- [ ] Half Adder
- [ ] Full Adder
- [ ] Half Subtractor
- [ ] Full Subtractor
- [x] JK Flip-Flop
- [x] T Flip-Flop
- [ ] Binary Counter (Async/Sync)
- [ ] 4-bit Register (PIPO, SISO, SIPO, PISO)
- [x] Multiplexer (MUX)
- [x] Demultiplexer (DEMUX)
- [ ] Encoder
- [ ] Decoder
- [ ] 7-Segment Display (with BCD decoder)

## 7. 💾 Custom Component System
- [x] Group multiple gates and wires
- [x] Allow saving as reusable custom components
- [x] Assign input/output interface pins
- [x] Load and place saved components like basic gates

## 8. 💽 Persistence (Save/Load)
- [x] Save entire circuit to `.json` or `.tres`
- [x] Load and reconstruct gates/wires from file

## 9. 🎨 Visual Features
- [x] Grid snapping and alignment
- [x] Signal-based color coding (e.g. green = 1, red = 0)
- [x] Pin labeling
- [x] UI scaling with zoom level

## 10. 🛠️ Debugging & Tools
- [ ] Optional waveform viewer

## 11. 🚀 Packaging & Deployment
- [ ] Export to desktop platforms (Windows/Linux/macOS)
- [ ] Optimize for performance (e.g., spatial hashing for wire detection)
- [ ] Optional: Export to web (HTML5 build)

## 12. Additional
- [ ] Change the Control buttons with Node2D instances for better performance
- [ ] Limit Max Length of Pin Naming and Gate Naming
- [ ] Adjust Bin Placement Based On Camera Zoom
- [ ] Test On Mobile
- [x] Redo Pin rendering to make it a dot and a line
- [x] Custom JK and T Flip Flop
- [ ] Ability To Save Gate with custom JK and T Flip Flop
- [ ] Sound Design
  - [ ] Lifting Up Gate
  - [ ] Place Gate
  - [ ] Creating Wire Connection
  - [ ] Press Switch
  - [ ] Delete Gate
  - [ ] Save Gate