class_name Pin2D extends StaticBody2D

#region Initial Positioning
@onready var canvas_node: CanvasNode = get_parent().get_parent().get_parent().get_node("Canvas Node")

func _ready():
	global_position = canvas_node.snap_to_grid(global_position)
	_update_color()
#endregion

#region Color Management
@export var color_default: Color
@export var color_connected: Color
@export var color_true: Color
var connected := false

func _update_color():
	var new_color = $PanelContainer.modulate
	if connected:
		new_color = color_connected
		if state:
			new_color = color_true
	else:
		new_color = color_default
	
	if new_color != $PanelContainer.modulate:
		var tween = get_tree().create_tween()
		tween.tween_property($PanelContainer, "modulate", new_color, 0.5)


#endregion

#region State Transfer
var state := false

func transfer_state():
	await get_tree().create_timer(SimulationManager.infinite_recursion_wait_time).timeout
	for wire in get_connected_wires():
		wire.state = state
		wire.update_color()
		if wire.output_pin and wire.output_pin != self:
			wire.output_pin.state = state
			await wire.output_pin.transfer_state()
		else:
			if gate is Gate2D:
				gate.input_pins[name] = state
				if gate.is_in_group("special_gates"):
					gate.custom_evaluate_expression()
				else:
					gate.evaluate_expression()
	_update_color()

func get_connected_wires():
	var res = []
	for body in $"Wire Detector".get_overlapping_areas():
		if body.get_parent() is Wire2D:
			res.append(body.get_parent())
	connected = res.size() > 0
	return res
#endregion

#region Wire Creation
@onready var wire_scene = preload("res://classes/wire 2d/wire_2d.tscn")
@onready var gate = get_parent().get_parent()
@onready var outline = $Outline

var is_input_pin = false
func _input(event):
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT and event.pressed and not SimulationManager.curr_active_wire:
			for body in canvas_node.detector.get_overlapping_bodies():
				# if mouse clicks on the pin
				if body == self and not is_input_pin:
					outline.visible = true
					# create a new wire with the input pin as the pin itself
					var wire = wire_scene.instantiate()
					wire.input_pin = self
					wire.add_point(global_position)
					wire.unrounded_points.append(global_position)
					connected = true
					canvas_node.get_parent().add_child(wire)
					SimulationManager.curr_active_wire = wire
					break
#endregion
