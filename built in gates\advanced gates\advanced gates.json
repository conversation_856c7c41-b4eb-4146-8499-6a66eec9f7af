[{"gate_name": "SR LATCH", "gate_inputs": ["s", "r"], "gate_outputs": ["q", "qn"], "nodes": [{"id": "n1", "type": "NOR", "inputs": ["s", "q"], "output": "qn"}, {"id": "n2", "type": "NOR", "inputs": ["r", "qn"], "output": "q"}]}, {"gate_name": "SR FLIP FLOP", "gate_inputs": ["clk", "s", "r"], "gate_outputs": ["q", "qn"], "nodes": [{"id": "n1", "type": "NOR", "inputs": ["s", "q"], "output": "qn"}, {"id": "n2", "type": "NOR", "inputs": ["r", "qn"], "output": "q"}]}, {"gate_name": "D FLIP FLOP", "gate_inputs": ["clk", "d"], "gate_outputs": ["q", "qn"], "nodes": [{"id": "97727285017_internal_0", "type": "NOT", "inputs": ["d"], "output": "r_55733913232"}, {"id": "55733913232_internal_0", "type": "NOR", "inputs": ["d", "q"], "output": "qn"}, {"id": "55733913232_internal_1", "type": "NOR", "inputs": ["r_55733913232", "qn"], "output": "q"}]}, {"gate_name": "JK FLIP FLOP", "gate_inputs": ["clk", "j", "k"], "gate_outputs": ["q", "qn"], "nodes": [{"id": "n1", "type": "JK", "inputs": ["j", "k"], "output": "q"}, {"id": "n2", "type": "NOT", "inputs": ["q"], "output": "qn"}]}, {"gate_name": "T FLIP FLOP", "gate_inputs": ["clk", "t"], "gate_outputs": ["q", "qn"], "nodes": [{"id": "56254006961_internal_0", "inputs": ["t", "t"], "output": "q", "type": "JK"}, {"id": "56254006961_internal_1", "inputs": ["q"], "output": "qn", "type": "NOT"}]}, {"gate_name": "4 TO 1 MUX", "gate_inputs": ["d1", "d2", "d3", "d4", "s1", "s2"], "gate_outputs": ["q"], "nodes": [{"id": "n1", "type": "NOT", "inputs": ["s1"], "output": "ns0"}, {"id": "n2", "type": "NOT", "inputs": ["s2"], "output": "ns1"}, {"id": "n3", "type": "AND", "inputs": ["ns1", "ns0", "d1"], "output": "t0"}, {"id": "n4", "type": "AND", "inputs": ["ns1", "s1", "d2"], "output": "t1"}, {"id": "n5", "type": "AND", "inputs": ["s2", "ns0", "d3"], "output": "t2"}, {"id": "n6", "type": "AND", "inputs": ["s2", "s1", "d4"], "output": "t3"}, {"id": "n7", "type": "OR", "inputs": ["t0", "t1", "t2", "t3"], "output": "q"}]}, {"gate_name": "1 TO 4 DEMUX", "gate_inputs": ["d", "s1", "s2"], "gate_outputs": ["q1", "q2", "q3", "q4"], "nodes": [{"id": "n1", "type": "NOT", "inputs": ["s1"], "output": "ns0"}, {"id": "n2", "type": "NOT", "inputs": ["s2"], "output": "ns1"}, {"id": "n3", "type": "AND", "inputs": ["d", "ns1", "ns0"], "output": "q1"}, {"id": "n4", "type": "AND", "inputs": ["d", "ns1", "s1"], "output": "q2"}, {"id": "n5", "type": "AND", "inputs": ["d", "s2", "ns0"], "output": "q3"}, {"id": "n6", "type": "AND", "inputs": ["d", "s2", "s1"], "output": "q4"}]}, {"gate_name": "4 BIT SHIFT REGISTER", "gate_inputs": ["s1", "d1", "d2", "d3", "d4", "sin_left", "sin_right", "clk", "s0"], "gate_outputs": ["q1", "q2", "q3", "q4"], "nodes": [{"id": "ff1", "type": "JK", "inputs": ["clk", "j1", "k1"], "output": "q1"}, {"id": "ff2", "type": "JK", "inputs": ["clk", "j2", "k2"], "output": "q2"}, {"id": "ff3", "type": "JK", "inputs": ["clk", "j3", "k3"], "output": "q3"}, {"id": "ff4", "type": "JK", "inputs": ["clk", "j4", "k4"], "output": "q4"}, {"id": "s0n", "type": "NOT", "inputs": ["s0"], "output": "s0n"}, {"id": "s1n", "type": "NOT", "inputs": ["s1"], "output": "s1n"}, {"id": "ff1_i0", "type": "AND", "inputs": ["q1", "s1n", "s0n"], "output": "ff1_i0"}, {"id": "ff1_i1", "type": "AND", "inputs": ["sin_right", "s1n", "s0"], "output": "ff1_i1"}, {"id": "ff1_i2", "type": "AND", "inputs": ["q2", "s1", "s0n"], "output": "ff1_i2"}, {"id": "ff1_i3", "type": "AND", "inputs": ["d1", "s1", "s0"], "output": "ff1_i3"}, {"id": "ff1_d", "type": "OR", "inputs": ["ff1_i0", "ff1_i1", "ff1_i2", "ff1_i3"], "output": "ff1_d"}, {"id": "ff2_i0", "type": "AND", "inputs": ["q2", "s1n", "s0n"], "output": "ff2_i0"}, {"id": "ff2_i1", "type": "AND", "inputs": ["q1", "s1n", "s0"], "output": "ff2_i1"}, {"id": "ff2_i2", "type": "AND", "inputs": ["q3", "s1", "s0n"], "output": "ff2_i2"}, {"id": "ff2_i3", "type": "AND", "inputs": ["d2", "s1", "s0"], "output": "ff2_i3"}, {"id": "ff2_d", "type": "OR", "inputs": ["ff2_i0", "ff2_i1", "ff2_i2", "ff2_i3"], "output": "ff2_d"}, {"id": "ff3_i0", "type": "AND", "inputs": ["q3", "s1n", "s0n"], "output": "ff3_i0"}, {"id": "ff3_i1", "type": "AND", "inputs": ["q2", "s1n", "s0"], "output": "ff3_i1"}, {"id": "ff3_i2", "type": "AND", "inputs": ["q4", "s1", "s0n"], "output": "ff3_i2"}, {"id": "ff3_i3", "type": "AND", "inputs": ["d3", "s1", "s0"], "output": "ff3_i3"}, {"id": "ff3_d", "type": "OR", "inputs": ["ff3_i0", "ff3_i1", "ff3_i2", "ff3_i3"], "output": "ff3_d"}, {"id": "ff4_i0", "type": "AND", "inputs": ["q4", "s1n", "s0n"], "output": "ff4_i0"}, {"id": "ff4_i1", "type": "AND", "inputs": ["q3", "s1n", "s0"], "output": "ff4_i1"}, {"id": "ff4_i2", "type": "AND", "inputs": ["sin_left", "s1", "s0n"], "output": "ff4_i2"}, {"id": "ff4_i3", "type": "AND", "inputs": ["d4", "s1", "s0"], "output": "ff4_i3"}, {"id": "ff4_d", "type": "OR", "inputs": ["ff4_i0", "ff4_i1", "ff4_i2", "ff4_i3"], "output": "ff4_d"}, {"id": "jk1_j", "type": "BUF_CONNECT", "inputs": ["ff1_d"], "output": "j1"}, {"id": "jk1_k", "type": "NOT", "inputs": ["ff1_d"], "output": "k1"}, {"id": "jk2_j", "type": "BUF_CONNECT", "inputs": ["ff2_d"], "output": "j2"}, {"id": "jk2_k", "type": "NOT", "inputs": ["ff2_d"], "output": "k2"}, {"id": "jk3_j", "type": "BUF_CONNECT", "inputs": ["ff3_d"], "output": "j3"}, {"id": "jk3_k", "type": "NOT", "inputs": ["ff3_d"], "output": "k3"}, {"id": "jk4_j", "type": "BUF_CONNECT", "inputs": ["ff4_d"], "output": "j4"}, {"id": "jk4_k", "type": "NOT", "inputs": ["ff4_d"], "output": "k4"}]}, {"gate_name": "COUNTER_4BIT", "gate_inputs": ["clk"], "gate_outputs": ["q1", "q2", "q3", "q4"], "nodes": [{"id": "ff1", "type": "T", "inputs": ["clk", "t1"], "output": "q1"}, {"id": "ff2", "type": "T", "inputs": ["clk", "t2"], "output": "q2"}, {"id": "ff3", "type": "T", "inputs": ["clk", "t3"], "output": "q3"}, {"id": "ff4", "type": "T", "inputs": ["clk", "t4"], "output": "q4"}, {"id": "const1", "type": "CONST1", "inputs": [], "output": "t1"}, {"id": "and1", "type": "AND", "inputs": ["q1"], "output": "t2"}, {"id": "and2", "type": "AND", "inputs": ["q1", "q2"], "output": "t3"}, {"id": "and3", "type": "AND", "inputs": ["q1", "q2", "q3"], "output": "t4"}]}, {"gate_name": "HALF ADDER", "gate_inputs": ["a", "b"], "gate_outputs": ["s", "c"], "nodes": [{"id": "n1", "type": "XOR", "inputs": ["a", "b"], "output": "s"}, {"id": "n2", "type": "AND", "inputs": ["a", "b"], "output": "c"}]}, {"gate_name": "FULL ADDER", "gate_inputs": ["a", "b", "cin"], "gate_outputs": ["s", "c"], "nodes": [{"id": "n1", "type": "XOR", "inputs": ["a", "b"], "output": "w1"}, {"id": "n2", "type": "XOR", "inputs": ["w1", "cin"], "output": "s"}, {"id": "n3", "type": "AND", "inputs": ["a", "b"], "output": "w2"}, {"id": "n4", "type": "AND", "inputs": ["b", "cin"], "output": "w3"}, {"id": "n5", "type": "AND", "inputs": ["a", "cin"], "output": "w4"}, {"id": "n6", "type": "OR", "inputs": ["w2", "w3", "w4"], "output": "c"}]}, {"gate_name": "HALF SUBTRACTOR", "gate_inputs": ["a", "b"], "gate_outputs": ["d", "bo"], "nodes": [{"id": "n1", "type": "XOR", "inputs": ["a", "b"], "output": "d"}, {"id": "n2", "type": "NOT", "inputs": ["a"], "output": "w1"}, {"id": "n3", "type": "AND", "inputs": ["w1", "b"], "output": "bo"}]}, {"gate_name": "FULL SUBTRACTOR", "gate_inputs": ["a", "b", "bin"], "gate_outputs": ["d", "bo"], "nodes": [{"id": "n1", "type": "XOR", "inputs": ["a", "b"], "output": "w1"}, {"id": "n2", "type": "XOR", "inputs": ["w1", "bin"], "output": "d"}, {"id": "n3", "type": "NOT", "inputs": ["a"], "output": "w2"}, {"id": "n4", "type": "AND", "inputs": ["w2", "b"], "output": "w3"}, {"id": "n5", "type": "NOT", "inputs": ["w1"], "output": "w4"}, {"id": "n6", "type": "AND", "inputs": ["w4", "bin"], "output": "w5"}, {"id": "n7", "type": "OR", "inputs": ["w3", "w5"], "output": "bo"}]}, {"color": "(1.0, 1.0, 1.0, 1.0)", "gate_inputs": ["a1", "a2", "a3", "a4", "b1", "b2", "b3", "b4"], "gate_name": "4-BIT ADDER", "gate_outputs": ["s1", "s2", "s3", "s4", "c"], "nodes": [{"id": "73870083829_internal_0", "inputs": ["a1", "b1"], "output": "73870083829_w1", "type": "XOR"}, {"id": "73870083829_internal_1", "inputs": ["73870083829_w1", "false"], "output": "s1", "type": "XOR"}, {"id": "73870083829_internal_2", "inputs": ["a1", "b1"], "output": "73870083829_w2", "type": "AND"}, {"id": "73870083829_internal_3", "inputs": ["b1", "false"], "output": "73870083829_w3", "type": "AND"}, {"id": "73870083829_internal_4", "inputs": ["a1", "false"], "output": "73870083829_w4", "type": "AND"}, {"id": "73870083829_internal_5", "inputs": ["73870083829_w2", "73870083829_w3", "73870083829_w4"], "output": "73870083829_c", "type": "OR"}, {"id": "80715187954_internal_0", "inputs": ["a2", "b2"], "output": "80715187954_w1", "type": "XOR"}, {"id": "80715187954_internal_1", "inputs": ["80715187954_w1", "cin_80715187954"], "output": "s2", "type": "XOR"}, {"id": "80715187954_internal_2", "inputs": ["a2", "b2"], "output": "80715187954_w2", "type": "AND"}, {"id": "80715187954_internal_3", "inputs": ["b2", "cin_80715187954"], "output": "80715187954_w3", "type": "AND"}, {"id": "80715187954_internal_4", "inputs": ["a2", "cin_80715187954"], "output": "80715187954_w4", "type": "AND"}, {"id": "80715187954_internal_5", "inputs": ["80715187954_w2", "80715187954_w3", "80715187954_w4"], "output": "80715187954_c", "type": "OR"}, {"id": "85479917272_internal_0", "inputs": ["a3", "b3"], "output": "85479917272_w1", "type": "XOR"}, {"id": "85479917272_internal_1", "inputs": ["85479917272_w1", "cin_85479917272"], "output": "s3", "type": "XOR"}, {"id": "85479917272_internal_2", "inputs": ["a3", "b3"], "output": "85479917272_w2", "type": "AND"}, {"id": "85479917272_internal_3", "inputs": ["b3", "cin_85479917272"], "output": "85479917272_w3", "type": "AND"}, {"id": "85479917272_internal_4", "inputs": ["a3", "cin_85479917272"], "output": "85479917272_w4", "type": "AND"}, {"id": "85479917272_internal_5", "inputs": ["85479917272_w2", "85479917272_w3", "85479917272_w4"], "output": "85479917272_c", "type": "OR"}, {"id": "92677343128_internal_0", "inputs": ["a4", "b4"], "output": "92677343128_w1", "type": "XOR"}, {"id": "92677343128_internal_1", "inputs": ["92677343128_w1", "cin_92677343128"], "output": "s4", "type": "XOR"}, {"id": "92677343128_internal_2", "inputs": ["a4", "b4"], "output": "92677343128_w2", "type": "AND"}, {"id": "92677343128_internal_3", "inputs": ["b4", "cin_92677343128"], "output": "92677343128_w3", "type": "AND"}, {"id": "92677343128_internal_4", "inputs": ["a4", "cin_92677343128"], "output": "92677343128_w4", "type": "AND"}, {"id": "92677343128_internal_5", "inputs": ["92677343128_w2", "92677343128_w3", "92677343128_w4"], "output": "c", "type": "OR"}]}]