import json
import sys
import os
sys.path.append(os.path.join(os.getcwd(), 'tests'))

# Import from the test file directly
exec(open('tests/test.py').read())

# Test just a simple JK flip-flop
circuits_json_string = """
[
    {
        "gate_name": "JK_TEST",
        "gate_inputs": ["clk", "j", "k"],
        "gate_outputs": ["q", "qn"],
        "nodes": [
            { "id": "jk1", "type": "JK", "inputs": ["clk", "j", "k"], "outputs": {"q": "q", "qn": "qn"} }
        ]
    }
]
"""

all_circuits_json = json.loads(circuits_json_string)
manager = CircuitManager(all_circuits_json)
jk_def = manager.get_circuit('JK_TEST')
jk = LogicGateSimulator(jk_def, manager)

print('Testing JK flip-flop:')
print(f'Initial state: {jk.flip_flop_states}')

# Test sequence: J=1, K=0 (should set Q=1)
print('\n--- Test 1: J=1, K=0 (Set) ---')
jk.execute({'clk': False, 'j': True, 'k': False})  # Setup
result = jk.execute({'clk': True, 'j': True, 'k': False})  # Rising edge
print(f'Result: {result}')
print(f'FF state: {jk.flip_flop_states}')

# Test sequence: J=0, K=1 (should reset Q=0)
print('\n--- Test 2: J=0, K=1 (Reset) ---')
jk.execute({'clk': False, 'j': False, 'k': True})  # Setup
result = jk.execute({'clk': True, 'j': False, 'k': True})  # Rising edge
print(f'Result: {result}')
print(f'FF state: {jk.flip_flop_states}')

# Test sequence: J=1, K=1 (should toggle)
print('\n--- Test 3: J=1, K=1 (Toggle) ---')
jk.execute({'clk': False, 'j': True, 'k': True})  # Setup
result = jk.execute({'clk': True, 'j': True, 'k': True})  # Rising edge
print(f'Result: {result}')
print(f'FF state: {jk.flip_flop_states}')

# Test sequence: J=0, K=0 (should hold)
print('\n--- Test 4: J=0, K=0 (Hold) ---')
jk.execute({'clk': False, 'j': False, 'k': False})  # Setup
result = jk.execute({'clk': True, 'j': False, 'k': False})  # Rising edge
print(f'Result: {result}')
print(f'FF state: {jk.flip_flop_states}')
