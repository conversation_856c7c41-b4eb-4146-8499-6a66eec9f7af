import json
import sys
import os
sys.path.append(os.path.join(os.getcwd(), 'tests'))

# Import from the test file directly
exec(open('tests/test.py').read())

# Test just the counter
circuits_json_string = """
[
    {
        "gate_name": "COUNTER_4BIT",
        "gate_inputs": ["clk"],
        "gate_outputs": ["q0", "q1", "q2", "q3"],
        "nodes": [
            { "id": "xor_true", "type": "XOR", "inputs": ["a", "a_n"], "output": "const_1" },
            { "id": "not_a", "type": "NOT", "inputs": ["a"], "output": "a_n" },
            { "id": "ff0", "type": "T", "inputs": ["clk", "const_1"], "outputs": {"q": "q0", "qn": "q0n"} },
            { "id": "ff1", "type": "T", "inputs": ["clk", "q0"], "outputs": {"q": "q1", "qn": "q1n"} },
            { "id": "and1", "type": "AND", "inputs": ["q0", "q1"], "output": "t2_in" },
            { "id": "ff2", "type": "T", "inputs": ["clk", "t2_in"], "outputs": {"q": "q2", "qn": "q2n"} },
            { "id": "and2", "type": "AND", "inputs": ["t2_in", "q2"], "output": "t3_in" },
            { "id": "ff3", "type": "T", "inputs": ["clk", "t3_in"], "outputs": {"q": "q3", "qn": "q3n"} }
        ]
    }
]
"""

all_circuits_json = json.loads(circuits_json_string)
manager = CircuitManager(all_circuits_json)
counter_def = manager.get_circuit('COUNTER_4BIT')
counter = LogicGateSimulator(counter_def, manager)

print('Initial state:')
print(f'Wires: {counter.wires}')
print(f'Flip-flop states: {counter.flip_flop_states}')

# Test first few cycles
for i in range(4):
    print(f'\n--- Cycle {i} ---')
    print('Before rising edge:')
    print(f'Clock states: {counter.prev_clk_states}')
    
    # Rising edge
    outputs = counter.execute({'clk': True})
    print('After rising edge:')
    print(f'Outputs: {outputs}')
    print(f'Flip-flop states: {counter.flip_flop_states}')
    print(f'Key wires: const_1={counter.wires.get("const_1")}, t2_in={counter.wires.get("t2_in")}, t3_in={counter.wires.get("t3_in")}')
    
    # Falling edge
    counter.execute({'clk': False})
